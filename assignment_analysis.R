# Install and load necessary packages
# install.packages("rtoot")
# install.packages("dplyr")
# install.packages("igraph") # New package for Question 2
library(tidyverse)
library(rtoot)
library(dplyr)
library(igraph) # Load igraph for graph analysis

# --- Question 1: Gathering the Users ---

# IMPORTANT: You need to authenticate with <PERSON><PERSON><PERSON>.
# Run the following line interactively in your R console and follow the prompts.
# This will open a browser window for authentication.
# auth_setup(instance = "mastodon.social", type = "user") 
# Replace "mastodon.social" with your preferred Mastodon instance if different.

# Once authenticated, your token should be saved or available in your R session.
# If you saved it to an environment variable, you might need to restart R or load it.
# For demonstration purposes, we'll assume a token is available.

# User's added code for instance information and #rstats test:
# This confirms that the rtoot package can connect to mastodon.social and retrieve data.
get_instance_general(instance = "mastodon.social")
get_instance_activity(instance = "mastodon.social")
get_instance_trends(instance = "mastodon.social")
get_timeline_hashtag(hashtag = "rstats", instance = "mastodon.social")


# Download toots containing the hashtag #WSUCOMP7025
# Note: The 'limit' parameter can be adjusted to retrieve more toots.
# For a real assignment, you might need to use pagination to get all relevant toots.

cat("Attempting to download toots with #WSUCOMP7025 from mastodon.social...\n")
toots_data <- tryCatch({
  # Explicitly using 'mastodon.social' as the instance.
  # Keeping anonymous = TRUE as it worked for #rstats.
  # If you are authenticated and still face issues, try setting anonymous = FALSE.
  get_timeline_hashtag(hashtag = "WSUCOMP7025", limit = 500, parse = TRUE, # Increased limit
                       instance = "mastodon.social", anonymous = TRUE) 
}, error = function(e) {
  message("Error downloading toots for #WSUCOMP7025: ", e$message)
  message("Possible reasons:")
  message("1. No public toots with #WSUCOMP7025 exist on mastodon.social.")
  message("2. Authentication issue (if you intended to use a user token).")
  message("3. Network connectivity problems.")
  return(NULL)
})

if (!is.null(toots_data) && nrow(toots_data) > 0) {
  cat("Successfully downloaded", nrow(toots_data), "toots for #WSUCOMP7025.\n")
  # Create a table showing each server and the count of toots from that server
  server_counts <- toots_data %>%
    mutate(server = sub("https://([^/]+).*", "\\1", url)) %>% # Extract server from URL
    group_by(server) %>%
    summarise(toot_count = n()) %>%
    arrange(desc(toot_count))

  cat("\nTable of toots per server for #WSUCOMP7025:\n")
  print(server_counts)
} else {
  cat("No toots found for #WSUCOMP7025 or an error occurred during download. Question 1 cannot be fully completed without data.\n")
  cat("Please ensure the hashtag is in use or try a different instance/authentication method.\n")
}


# --- Question 2: Following Graph ---

cat("\n--- Starting Question 2: Following Graph ---\n")

if (is.null(toots_data) || nrow(toots_data) == 0) {
  cat("Cannot proceed with Question 2: No toot data available from Question 1.\n")
  cat("Please ensure Question 1 successfully downloads toots with #WSUCOMP7025.\n")
} else {
  # IMPORTANT: For this section, you MUST be authenticated with a user token.
  # If you haven't already, run `auth_setup(instance = "mastodon.social", type = "user")`
  # and follow the browser prompts.
  
  cat("Extracting unique authors from downloaded toots...\n")
  # Extract unique author IDs
  # Assuming 'account.id' is the column containing author IDs
  # And 'account.acct' for their usernames (or 'account.display_name')
  cat("Extracting unique authors from downloaded toots...\n")
  # Unnest the 'account' column to access author details
  toots_data_unnested <- toots_data %>%
    unnest_wider(account, names_sep = "_")

  # Extract unique author IDs and account names
  unique_authors <- toots_data_unnested %>%
    distinct(account_id, account_acct) %>%
    rename(author_id = account_id, author_acct = account_acct)

  if (nrow(unique_authors) == 0) {
    cat("No unique authors could be extracted after unnesting 'account' column. Cannot proceed with graph creation.\n")
    return() # Exit the else block
  }

  cat("Found", nrow(unique_authors), "unique authors.\n")
  
  # Initialize data structures to store following relationships and all account info
  following_edges <- data.frame(follower = character(), followed = character(), stringsAsFactors = FALSE)
  all_followed_accounts_info <- list() # To store info of all followed accounts

  cat("Downloading following details for each author (this may take time and requires user authentication)...\n")
  
  # Loop through each author to get who they follow
  # Limiting to a small number of authors for demonstration to avoid excessive API calls and rate limits.
  # For the full assignment, you would iterate through all unique_authors.
  max_authors_to_process = nrow(unique_authors) # Process all unique authors
  cat("WARNING: Processing ALL", max_authors_to_process, "authors. This may take a very long time and is highly susceptible to Mastodon API rate limits. Be prepared for long execution times or potential errors.\n")
  
  for (i in 1:max_authors_to_process) {
    author_id <- unique_authors$author_id[i]
    author_acct <- unique_authors$author_acct[i]
    cat(paste0("Fetching who '", author_acct, "' (ID: ", author_id, ") follows... (", i, "/", max_authors_to_process, ")\n"))
    
    # get_account_following requires a user token.
    # If no token is available, this call will likely fail.
    # Adding a tryCatch for robustness.
    followed_accounts <- tryCatch({
      get_account_following(id = author_id, limit = 100, parse = TRUE, 
                            token = NULL, # Assumes token is loaded or available
                            retryonratelimit = TRUE)
    }, error = function(e) {
      message(paste0("Error fetching following for ", author_acct, ": ", e$message))
      message("Ensure you have a valid user token and are authenticated.")
      return(NULL)
    })
    
    if (!is.null(followed_accounts) && nrow(followed_accounts) > 0) {
      temp_edges <- data.frame(
        follower = rep(author_id, nrow(followed_accounts)),
        followed = followed_accounts$id,
        stringsAsFactors = FALSE
      )
      following_edges <- bind_rows(following_edges, temp_edges)
      cat(paste0("  Found ", nrow(followed_accounts), " accounts followed by ", author_acct, ".\n"))
      
      # Collect info of followed accounts
      all_followed_accounts_info[[length(all_followed_accounts_info) + 1]] <- followed_accounts %>% 
        select(id, acct) # Assuming followed_accounts has 'id' and 'acct'
    } else {
      cat(paste0("  No accounts found or error fetching following for ", author_acct, ".\n"))
    }
    
    # Add a small delay to respect API rate limits (CRITICAL for many authors)
    Sys.sleep(1) # Re-added delay to prevent hitting rate limits
  }
  
  if (nrow(following_edges) == 0) {
    cat("No following relationships found. Cannot create graph.\n")
  } else {
    cat("\nCreating directed graph...\n")
    # Create a directed graph
    
    # Combine unique authors from toots_data and all collected followed accounts
    # Ensure all unique authors and followed accounts are included as vertices
    all_accounts_info <- bind_rows(
      unique_authors %>% select(id = author_id, acct = author_acct),
      bind_rows(all_followed_accounts_info) # Combine all collected followed accounts info
    ) %>% distinct(id, .keep_all = TRUE)
    
    # Create the graph
    g <- graph_from_data_frame(following_edges, directed = TRUE, vertices = all_accounts_info$id)
    
    # Add account names as vertex attributes for plotting
    V(g)$name <- all_accounts_info$acct[match(V(g)$name, all_accounts_info$id)]
    
    cat("Graph created with", vcount(g), "vertices and", ecount(g), "edges.\n")
    
    # Compute graph components
    cat("Computing graph components...\n")
    components_result <- components(g, mode = "strong") # Use "strong" for directed graphs
    
    cat("Number of components:", components_result$no, "\n")
    cat("Sizes of components:\n")
    print(sort(components_result$csize, decreasing = TRUE))
    
    # Identify the largest component
    largest_component_id <- which.max(components_result$csize)
    largest_component_vertices <- V(g)[components_result$membership == largest_component_id]
    largest_component_subgraph <- induced_subgraph(g, largest_component_vertices)
    
    cat("\nLargest component has", vcount(largest_component_subgraph), " vertices.\n")
    
    # Plot the largest component
    cat("Plotting the largest component (output will be in R plot window)...\n")
    plot(largest_component_subgraph, 
         vertex.label.cex = 0.6, # Slightly smaller labels for potentially more nodes
         vertex.size = 6, # Smaller nodes for potentially more nodes
         vertex.color = "skyblue", # Node color
         vertex.frame.color = "steelblue", # Node border color
         vertex.label.color = "darkred", # Label color for contrast
         edge.arrow.size = 0.2, # Smaller arrows
         edge.color = "lightgray", # Lighter edge color
         edge.width = 0.5, # Thinner edges
         main = "Largest Component of Following Graph (#WSUCOMP7025 Authors)",
         sub = paste0("Nodes: ", vcount(largest_component_subgraph), ", Edges: ", ecount(largest_component_subgraph)),
         layout = layout_with_fr) # Fruchterman-Reingold layout for better visualization
    
    # Comment on the structure of the graph
    cat("\n--- Comment on the structure of the graph ---\n")
    cat("The structure of the following graph, particularly its largest component, provides insights into the student social network.\n")
    cat("A high number of components or many small components suggests a fragmented network with isolated groups.\n")
    cat("A large single component indicates a more connected network where many students are reachable from each other.\n")
    cat("The density and presence of central nodes (high in-degree) would further reveal influential students or tightly-knit groups.\n")
    cat("Visual inspection of the plot can show clusters or 'hubs' of activity.\n")
    cat("For the Department of Education, a more connected network (larger components) might suggest better information flow and peer support, potentially impacting learning outcomes.\n")
    
    # Note for the remainder of the project
    cat("\nNOTE: For the remainder of the project, analysis will focus on the largest component of this graph.\n")
  }
}

# --- Question 3: Graph Statistics ---

cat("\n--- Starting Question 3: Graph Statistics ---\n")

if (!exists("largest_component_subgraph") || vcount(largest_component_subgraph) == 0) {
  cat("Cannot proceed with Question 3: Largest component graph is not available or is empty.\n")
  cat("Please ensure Question 2 successfully creates a non-empty largest component.\n")
} else {
  # Compute diameter
  graph_diameter <- diameter(largest_component_subgraph, directed = TRUE, unconnected = TRUE)
  cat(paste0("Diameter of the largest component: ", graph_diameter, "\n"))
  
  # Compute density
  graph_density <- edge_density(largest_component_subgraph, loops = FALSE)
  cat(paste0("Density of the largest component: ", round(graph_density, 4), "\n"))
  
  # Plot in-degree distribution
  in_degrees <- degree(largest_component_subgraph, mode = "in")
  
  cat("\nPlotting in-degree distribution (output will be in R plot window)...\n")
  hist(in_degrees, 
       breaks = seq(min(in_degrees)-0.5, max(in_degrees)+0.5, by = 1), 
       main = "In-Degree Distribution of Largest Component",
       xlab = "In-Degree",
       ylab = "Frequency",
       col = "lightgreen",
       border = "darkgreen")
  
  # Estimate Power Law coefficient (c)
  # Using power.law.fit from igraph
  if (length(in_degrees[in_degrees > 0]) > 1) { # Need at least 2 non-zero degrees to fit
    power_law_fit <- power.law.fit(in_degrees[in_degrees > 0]) # Fit only on non-zero degrees
    
    if (!is.null(power_law_fit) && !is.null(power_law_fit$alpha)) {
      cat(paste0("\nEstimated Power Law coefficient (c): ", round(power_law_fit$alpha, 4), "\n"))
      
      if (!is.null(power_law_fit$p) && is.numeric(power_law_fit$p)) {
        cat(paste0("Power Law fit p-value: ", round(power_law_fit$p, 4), "\n"))
      } else {
        cat("Power Law fit p-value not available or not numeric.\n")
      }
      
      cat("\n--- Explanation of Power Law Coefficient ---\n")
      cat("The Power Law coefficient (c or alpha) describes the shape of the degree distribution.\n")
      cat("In social networks, a power-law distribution (also known as a scale-free network) indicates that a few nodes (hubs) have a very high number of connections (high in-degree),\n")
      cat("while most nodes have only a few connections. This is characteristic of many real-world social networks.\n")
      cat("A smaller 'c' value (typically between 2 and 3 for many real-world networks) suggests a more pronounced 'rich-get-richer' phenomenon, where highly connected nodes attract even more connections.\n")
      cat("For the Department of Education, a power-law distribution in the student social network would imply that a small number of students are highly influential or central,\n")
      cat("potentially acting as key disseminators of information or support. Strategies for new students could target these hubs.\n")
    } else {
      cat("\nPower Law fit could not be performed or returned unexpected results.\n")
      cat("This might happen if the data does not conform to a power-law distribution or is too sparse.\n")
    }
  } else {
    cat("\nNot enough non-zero in-degrees to estimate Power Law coefficient.\n")
    cat("This usually happens if the graph is very small or sparse.\n")
  }
}

# --- Question 4: Investigating Homophily ---
cat("\n--- Starting Question 4: Investigating Homophily ---\n")

if (!exists("largest_component_subgraph") || vcount(largest_component_subgraph) == 0) {
  cat("Cannot proceed with Question 4: No largest component available.\n")
} else {
  # Step 1: Read lunch location data
  lunch_data <- tryCatch({
    read_csv("/Users/<USER>/Documents/Western_Sydney_Univeristy_MBA/QUARTER 2 : 2025/SOCIAL MEDIA/Assignment/lunch_location2025.csv")
  }, error = function(e) {
    message("Error reading lunch_location2025.csv: ", e$message)
    return(NULL)
  })

  if (is.null(lunch_data) || nrow(lunch_data) == 0) {
    cat("Lunch location data not loaded. Cannot proceed.\n")
  } else {
    cat("Successfully loaded lunch location data with", nrow(lunch_data), "entries.\n")
    
    # Step 2: Create subgraph of people with lunch location data
    # First, map lunch locations to vertices
    user_locations <- setNames(lunch_data$Location, lunch_data$Username)
    
    # Add lunch_location attribute to vertices
    V(largest_component_subgraph)$lunch_location <- user_locations[V(largest_component_subgraph)$name]
    
    # Create subgraph of only people with lunch location data
    people_with_location <- !is.na(V(largest_component_subgraph)$lunch_location)
    
    if (sum(people_with_location) > 1) {
      people_subgraph <- induced_subgraph(
        largest_component_subgraph, 
        which(people_with_location)
      )
      
      cat("Created subgraph of", vcount(people_subgraph), "people with lunch location data.\n")
      
      # Step 3: Test for homophily in this specific subgraph
      assortativity_coeff <- assortativity_nominal(
        graph = people_subgraph, 
        types = as.factor(V(people_subgraph)$lunch_location), 
        directed = TRUE
      )
      
      cat(paste0("\nAssortativity Coefficient (Homophily based on Lunch Location): ", 
                round(assortativity_coeff, 4), "\n"))
      
      # Step 4: Calculate percentage of same-location connections
      edge_list <- as_data_frame(people_subgraph, what = "edges")
      edge_list$from_location <- V(people_subgraph)$lunch_location[edge_list$from]
      edge_list$to_location <- V(people_subgraph)$lunch_location[edge_list$to]
      
      same_location_edges <- sum(edge_list$from_location == edge_list$to_location)
      total_edges <- nrow(edge_list)
      
      if (total_edges > 0) {
        homophily_percentage <- (same_location_edges / total_edges) * 100
        unique_locations <- unique(V(people_subgraph)$lunch_location)
        random_expectation <- 100 / length(unique_locations)
        
        cat(paste0("\nHomophily Percentage: ", round(homophily_percentage, 2), 
                  "% of connections are between users with the same lunch location.\n"))
        cat(paste0("Random expectation: ~", round(random_expectation, 2), "%\n"))
      }
      
      # Step 5: Interpret results
      cat("\n--- Does the subgraph exhibit homophily? ---\n")
      if (assortativity_coeff > 0.3) {
        cat("YES - STRONG HOMOPHILY: The subgraph strongly exhibits homophily.\n")
        cat("Students strongly prefer to follow others who share their lunch location.\n")
      } else if (assortativity_coeff > 0) {
        cat("YES - WEAK HOMOPHILY: The subgraph exhibits weak homophily.\n")
        cat("Students show a slight preference for following others with the same lunch location.\n")
      } else if (assortativity_coeff < -0.3) {
        cat("NO - STRONG HETEROPHILY: The subgraph exhibits strong heterophily.\n")
        cat("Students strongly prefer to follow others with different lunch locations.\n")
      } else {
        cat("NO - WEAK HETEROPHILY OR RANDOM: The subgraph does not exhibit homophily.\n")
        cat("Students show little preference based on lunch location.\n")
      }
      
      # Step 6: Visualize the subgraph
      unique_locations <- unique(V(people_subgraph)$lunch_location)
      color_palette <- rainbow(length(unique_locations))
      names(color_palette) <- unique_locations
      vertex_colors <- color_palette[V(people_subgraph)$lunch_location]
      
      plot(people_subgraph, 
           vertex.size = 8, 
           vertex.color = vertex_colors,
           vertex.label = NA,
           edge.arrow.size = 0.3,
           main = "Subgraph of People with Lunch Location Data",
           sub = paste0("Assortativity: ", round(assortativity_coeff, 4)),
           layout = layout_with_fr)
      
      legend("bottomleft", legend = unique_locations, fill = color_palette, 
             bty = "n", cex = 0.8, title = "Lunch Location")
      
      # Step 7: Conclusion for Department of Education
      cat("\n--- Conclusion for Department of Education ---\n")
      if (assortativity_coeff > 0 && homophily_percentage > random_expectation) {
        cat("The data supports the Department's belief that people who have lunch in the same location have similar preferences.\n")
        cat("The subgraph of people exhibits homophily based on lunch location.\n")
      } else {
        cat("The data does not support the Department's belief about lunch location preferences.\n")
        cat("The subgraph of people does not exhibit significant homophily based on lunch location.\n")
      }
    } else {
      cat("Not enough people with lunch location data to create a meaningful subgraph.\n")
    }
  }
}
